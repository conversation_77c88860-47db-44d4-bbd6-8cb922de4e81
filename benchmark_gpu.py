#!/usr/bin/env python3
"""
Benchmark script to compare CPU vs GPU performance for app2.py operations
"""

import time
import numpy as np
import faiss

# Try to import GPU libraries
try:
    import cupy as cp
    GPU_AVAILABLE = True
except ImportError:
    GPU_AVAILABLE = False

def benchmark_vector_normalization(embeddings, iterations=10):
    """Benchmark vector normalization CPU vs GPU"""
    print(f"\nBenchmarking vector normalization ({embeddings.shape})...")
    
    # CPU benchmark
    cpu_times = []
    for _ in range(iterations):
        start = time.time()
        cpu_result = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]
        cpu_times.append(time.time() - start)
    
    cpu_avg = np.mean(cpu_times)
    print(f"CPU average: {cpu_avg:.4f}s")
    
    if GPU_AVAILABLE:
        # GPU benchmark
        gpu_times = []
        for _ in range(iterations):
            start = time.time()
            gpu_embeddings = cp.asarray(embeddings)
            norms = cp.linalg.norm(gpu_embeddings, axis=1, keepdims=True)
            gpu_result = gpu_embeddings / norms
            gpu_result_cpu = cp.asnumpy(gpu_result)
            gpu_times.append(time.time() - start)
        
        gpu_avg = np.mean(gpu_times)
        speedup = cpu_avg / gpu_avg
        print(f"GPU average: {gpu_avg:.4f}s")
        print(f"GPU speedup: {speedup:.2f}x")
        
        # Verify results are similar
        diff = np.mean(np.abs(cpu_result - gpu_result_cpu))
        print(f"Result difference: {diff:.8f}")
    else:
        print("GPU not available for comparison")

def benchmark_faiss_index(embeddings, iterations=5):
    """Benchmark FAISS index creation and search"""
    print(f"\nBenchmarking FAISS operations ({embeddings.shape})...")
    
    # CPU FAISS
    cpu_times = []
    for _ in range(iterations):
        start = time.time()
        cpu_index = faiss.IndexFlatIP(embeddings.shape[1])
        cpu_index.add(embeddings)
        
        # Test search
        query = embeddings[:1]
        scores, indices = cpu_index.search(query, 10)
        cpu_times.append(time.time() - start)
    
    cpu_avg = np.mean(cpu_times)
    print(f"CPU FAISS average: {cpu_avg:.4f}s")
    
    # GPU FAISS (if available)
    try:
        res = faiss.StandardGpuResources()
        gpu_times = []
        
        for _ in range(iterations):
            start = time.time()
            cpu_index = faiss.IndexFlatIP(embeddings.shape[1])
            gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)
            gpu_index.add(embeddings)
            
            # Test search
            query = embeddings[:1]
            scores, indices = gpu_index.search(query, 10)
            gpu_times.append(time.time() - start)
        
        gpu_avg = np.mean(gpu_times)
        speedup = cpu_avg / gpu_avg
        print(f"GPU FAISS average: {gpu_avg:.4f}s")
        print(f"GPU speedup: {speedup:.2f}x")
        
    except Exception as e:
        print(f"GPU FAISS not available: {e}")

def run_benchmarks():
    """Run all benchmarks"""
    print("GPU Performance Benchmark for app2.py")
    print("=" * 50)
    
    # Test different sizes
    sizes = [
        (100, 1536),   # Small document
        (500, 1536),   # Medium document  
        (1000, 1536),  # Large document
    ]
    
    for rows, cols in sizes:
        print(f"\n--- Testing with {rows} embeddings of {cols} dimensions ---")
        
        # Generate random embeddings (similar to OpenAI embeddings)
        embeddings = np.random.randn(rows, cols).astype(np.float32)
        
        # Benchmark normalization
        benchmark_vector_normalization(embeddings)
        
        # Benchmark FAISS
        benchmark_faiss_index(embeddings)
    
    print("\n" + "=" * 50)
    print("Benchmark complete!")
    
    if GPU_AVAILABLE:
        print("\n✓ GPU acceleration is working")
        print("Your app2.py will automatically use GPU when available")
    else:
        print("\n⚠ GPU acceleration not available")
        print("Install GPU dependencies with: python install_gpu_dependencies.py")

if __name__ == "__main__":
    run_benchmarks()
